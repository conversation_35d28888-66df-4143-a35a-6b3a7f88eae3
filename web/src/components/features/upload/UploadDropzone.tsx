import { UploadIcon } from "lucide-react";
import { Dropzone, DropzoneEmptyState, DropzoneContent } from "../../ui/shadcn-io/dropzone";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { TypographyP } from "@/components/ui/typography";
import { useFileStore } from "@/stores/useFileStore";
import type { ParsedResultWithFilename } from "@/types/parsed.types";

export const UploadDropzone = ({
  onSuccessfulUpload,
}: {
  onSuccessfulUpload: (files: ParsedResultWithFilename[]) => void;
}) => {
  const fileStore = useFileStore();

  const handleDrop = async (files: File[]) => {
    const result = await fileStore.parse(files);

    if (result?.unsuccessfullyParsed.length) {
      result.unsuccessfullyParsed.forEach((parseError) => {
        toast("Failed to parse file", {
          description: `${parseError.file.name || "Unnamed file"}: ${parseError.error}`,
        });
      });
    }

    if (result?.successfullyParsed) {
      onSuccessfulUpload(result?.successfullyParsed);
    }
  };

  const handleError = (error: Error) => {
    toast("File type invalid", {
      description: error.message,
    });
  };

  return (
    <Dropzone
      onDrop={handleDrop}
      onError={handleError}
      src={fileStore.rawFiles}
      className="cursor-pointer"
      maxFiles={10}
    >
      <DropzoneEmptyState>
        <div className="flex w-full items-center gap-4 p-8">
          <div className="flex size-16 items-center justify-center rounded-lg bg-muted text-muted-foreground">
            <UploadIcon size={24} />
          </div>
          <div className="text-left">
            <p className="font-medium text-sm">Upload a file</p>
            <p className="text-muted-foreground text-xs">Drag and drop or click to upload</p>
          </div>
        </div>
      </DropzoneEmptyState>
      <DropzoneContent>
        {fileStore.progress !== undefined ? (
          <>
            <TypographyP>Uploading</TypographyP>
            <Skeleton className="h-[20px] w-full rounded-full" />
          </>
        ) : null}
      </DropzoneContent>
    </Dropzone>
  );
};

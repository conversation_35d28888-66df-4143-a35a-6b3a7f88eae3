import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
  Card<PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TypographyP } from "@/components/ui/typography";
import type { FileClassificationResult } from "@acr/common/types/classify.types";
import { LinkIcon } from "lucide-react";

export const ClassifyResponseCard = ({ result }: { result: FileClassificationResult }) => {
  console.log(result);
  const tableHeaders = Object.entries(result.rows[0]).map(([key]) => key);

  return (
    <Card className="transition-all ease-in-out delay-150 duration-300">
      <CardHeader>
        <CardTitle>
          <span className="flex items-center gap-2">
            <LinkIcon size={16} />
            {result.fileName}
          </span>
        </CardTitle>
        <CardDescription>
          <TypographyP>
            Review the details for the uploaded file and save or discard
          </TypographyP>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              {tableHeaders.map((header) => (
                <TableHead key={header}>{header}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {result.rows.map((row, index) => {
              <TableRow key={index}>
                <TableCell></TableCell>
              </TableRow>
            })}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="justify-end gap-4">
        <Button variant="secondary">Cancel</Button>
        <Button>Confirm</Button>
      </CardFooter>
    </Card>
  );
};

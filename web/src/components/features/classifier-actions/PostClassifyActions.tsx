import type { BatchClassifyResponse } from "@acr/common/types/classify.types";
import { ClassifyResponseCard } from "./ClassifyResponseCard";

export const PostClassifyActions = ({
  classifierResponse,
}: {
  classifierResponse: BatchClassifyResponse;
}) => {
  return (
    <div className="flex flex-col gap-4">
      {classifierResponse.results.map((result) => (
        <ClassifyResponseCard result={result} key={result.fileName} />
      ))}
    </div>
  );
};

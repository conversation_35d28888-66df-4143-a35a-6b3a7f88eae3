import { create } from "zustand";
import { persist } from "zustand/middleware";
import { toast } from "sonner";
import { parseFile } from "@/utils/parse";
import type { ParsedResultWithFilename, ParseError, ParseResult } from "@/types/parsed.types";
import type {
  BatchClassifyResponse,
  FileClassificationResult,
} from "@acr/common/types/classify.types";
import axios from "axios";

interface FileState {
  progress: number | undefined;
  rawFiles: File[];
  successfullyParsed: ParsedResultWithFilename[];
  unsuccessfullyParsed: ParseError[];
  classificationResults: FileClassificationResult[];
  parse: (files: File[]) => Promise<ParseResult | undefined>;
  classify: (files: ParsedResultWithFilename[]) => Promise<BatchClassifyResponse | undefined>;
}

export const useFileStore = create<FileState>()(
  persist(
    (set) => ({
      progress: undefined,
      rawFiles: [],
      successfullyParsed: [],
      unsuccessfullyParsed: [],
      classificationResults: [],
      parse: async (files) => {
        set({ progress: 0 });
        set({ rawFiles: files });
        set({ successfullyParsed: [], unsuccessfullyParsed: [] });

        const successfullyParsed: ParsedResultWithFilename[] = [];
        const unsuccessfullyParsed: ParseError[] = [];

        try {
          const results = await Promise.allSettled(
            files.map(async (file) => {
              try {
                const parsed = await parseFile(file);
                return { success: true as const, data: parsed, file };
              } catch (error) {
                const errorMessage =
                  error instanceof Error ? error.message : "Unknown parsing error";
                return { success: false as const, error: errorMessage, file };
              }
            }),
          );

          results.forEach((result) => {
            if (result.status === "fulfilled") {
              if (result.value.success) {
                successfullyParsed.push({
                  data: result.value.data,
                  fileName: result.value.file.name,
                });
              } else {
                unsuccessfullyParsed.push({
                  file: result.value.file,
                  error: result.value.error,
                });
              }
            }
          });

          set({ successfullyParsed, unsuccessfullyParsed });

          return {
            successfullyParsed,
            unsuccessfullyParsed,
          };
        } catch (error: unknown) {
          toast("Failed to parse files", {
            description: (error as Error).message,
          });
        } finally {
          set({ progress: undefined });
        }
      },
      classify: async (files): Promise<BatchClassifyResponse | undefined> => {
        set({ progress: 0, classificationResults: [] });

        try {
          const batchRequest = {
            files: files.map((fileWithName, index) => ({
              fileIndex: index,
              fileName: fileWithName.fileName,
              rows: fileWithName.data,
            })),
          };

          const result = await axios.post<BatchClassifyResponse>(
            `${import.meta.env.VITE_API_URL}/v1/public/classify`,
            batchRequest,
          );

          set({ classificationResults: result.data.results });

          return result.data;
        } catch (error: unknown) {
          toast("Failed to classify files", {
            description: (error as Error).message,
          });
          return undefined;
        } finally {
          set({ progress: undefined });
        }
      },
    }),
    {
      name: "file-store",
      partialize: () => ({}),
    },
  ),
);

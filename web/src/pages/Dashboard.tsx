import { UploadDropzone } from "@/components/features/upload/UploadDropzone";
import { Progress } from "@/components/ui/progress";
import { TypographyH1, TypographyP } from "@/components/ui/typography";
import { useFileStore } from "@/stores/useFileStore";
import { UploadIcon } from "lucide-react";
import { type ParsedResultWithFilename } from "@/types/parsed.types";
import { useState } from "react";
import type { BatchClassifyResponse } from "@acr/common/types/classify.types";
import { PostClassifyActions } from "@/components/features/classifier-actions/PostClassifyActions";

export const Dashboard = () => {
  const fileStore = useFileStore();

  const [classifierResponse, setClassifierResponse] = useState<BatchClassifyResponse | undefined>();

  const handleSuccessfulUpload = async (files: ParsedResultWithFilename[]) => {
    const result = await fileStore.classify(files);
    setClassifierResponse(result);
  };

  return (
    <div className="py-2 flex flex-col gap-4">
      <div>
        <span className="flex items-center gap-2">
          <UploadIcon />
          <TypographyH1>Upload</TypographyH1>
        </span>
        <TypographyP>Drop files here to upload</TypographyP>
      </div>

      <UploadDropzone onSuccessfulUpload={handleSuccessfulUpload} />

      {fileStore.progress ? <Progress value={fileStore.progress} /> : null}

      {classifierResponse ? <PostClassifyActions classifierResponse={classifierResponse} /> : null}
    </div>
  );
};

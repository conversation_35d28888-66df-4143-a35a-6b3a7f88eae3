import { z } from "zod";
import {
  classifyFarmerSchema,
  classifyActivitySchema,
  classifyContractSchema,
  batchClassifySchema,
} from "../schemas/classify";

export type TableType = "t_farmer" | "t_activity" | "t_payment_contract";

export type BatchClassifyRequest = z.infer<typeof batchClassifySchema>;
export type ClassifyFarmer = z.infer<typeof classifyFarmerSchema>;
export type ClassifyActivity = z.infer<typeof classifyActivitySchema>;
export type ClassifyContract = z.infer<typeof classifyContractSchema>;

export interface FileClassificationResult {
  fileIndex: number;
  fileName?: string;
  suggestedTables: TableType[];
  rows: Record<string, unknown>[];
  success: boolean;
  error?: string;
}

export interface BatchClassifyResponse {
  results: FileClassificationResult[];
  totalFiles: number;
  completedFiles: number;
}

export type AnyClassifiedRow =
  | ClassifyFarmer
  | ClassifyActivity
  | ClassifyContract;

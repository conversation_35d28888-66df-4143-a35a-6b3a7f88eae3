import { BaseError } from "@/utils/errors";
import { logger } from "@/utils/logger";
import { NextFunction, Request, Response } from "express";
import { ZodError } from "zod";

export const defaultErrorHandler = (
  error: BaseError,
  _req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (
    process.env.NODE_ENV === "development" ||
    process.env.NODE_ENV === "test"
  ) {
    console.error("ROUTER ERROR:", error);
    logger.error("ROUTER ERROR:", error);
  }

  if (error instanceof ZodError) {
    logger.error("Zod Error", error.issues);

    res.status(400);
    res.json({
      status_code: error.statusCode,
      message: "Invalid request body",
      details: error.issues,
    });
  } else {
    res.status(error.statusCode || 500);
    res.json({
      status_code: error.statusCode,
      message: error.message,
      details: error.details,
    });
  }

  next();
};

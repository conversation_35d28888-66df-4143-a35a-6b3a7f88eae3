import express, { Request, Response, NextFunction } from "express";
import { validateRequest } from "@/middleware/validateRequest";
import { batchClassifySchema } from "@acr/common/schemas/classify";
import { classifyBatch } from "@/domain/classify";
import {
  BatchClassifyRequest,
  BatchClassifyResponse,
} from "@acr/common/types/classify.types";

const router = express.Router();

router.post(
  "/",
  validateRequest("POST", batchClassifySchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = req.validatedAttributes as BatchClassifyRequest;
      const result: BatchClassifyResponse = classifyBatch(validatedData);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  },
);

export const classifierRoutes = router;

import express, { Request, Response, NextFunction } from "express";
import { validateRequest } from "@/middleware/validateRequest";
import { getFarmerGroupsSchema } from "@acr/common/schemas/farmerGroup";
import { farmerGroupActions } from "@/domain/farmerGroup";
import { GetFarmerGroups } from "@acr/common/types/farmerGroup.types";

const router = express.Router();

router.get(
  "/",
  validateRequest("GET", getFarmerGroupsSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const farmerGroups = await farmerGroupActions.get(
        req.validatedAttributes as GetFarmerGroups,
      );

      res.status(200).json(farmerGroups);
    } catch (error: unknown) {
      next(error);
    }
  },
);

export const farmerGroupsRoutes = router;

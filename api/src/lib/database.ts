import { ServerError } from "@/utils/errors";
import { logger } from "@/utils/logger";
import { DatabaseError, Pool, QueryResult } from "pg";

export type Query = {
  query: string;
  values?: any[];
};

export const query = async ({ query, values }: Query): Promise<QueryResult> => {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  pool.on("connect", (client) => {
    client.on("notice", (msg) => logger.info("DB Notice: ", msg));
  });

  try {
    const client = await pool.connect();

    const result = await client.query(query, values);

    client.release();

    return result;
  } catch (error) {
    throw ServerError(error as DatabaseError);
  }
};

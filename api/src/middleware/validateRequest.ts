import { NextFunction, Request, Response } from "express";
import { ZodObject, ZodSchema } from "zod";

export type AcceptableMethod = "GET" | "POST" | "PATCH" | "PUT" | "DELETE";

export const validateRequest = (
  method: AcceptableMethod,
  schema: ZodObject | ZodSchema,
) => {
  return (req: Request, _res: Response, next: NextFunction) => {
    try {
      const attributes = method === "GET" ? req.query : req.body;

      const validatedAttributes = schema.parse(attributes);

      req.validatedAttributes = validatedAttributes as Record<string, unknown>;

      next();
    } catch (error: unknown) {
      next(error);
    }
  };
};

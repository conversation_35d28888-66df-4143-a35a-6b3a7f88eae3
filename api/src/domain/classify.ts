import {
  TableType,
  FileClassificationResult,
  BatchClassifyRequest,
  BatchClassifyResponse,
} from "@acr/common/types/classify.types";
import {
  classifyActivitySchema,
  classifyContractSchema,
  classifyFarmerSchema,
} from "@acr/common/schemas/classify";

const classifyFileRows = (rows: Record<string, unknown>[]): TableType[] => {
  const suggestions: TableType[] = [];

  if (rows.every((r) => classifyFarmerSchema.safeParse(r).success)) {
    suggestions.push("t_farmer");
  }
  if (rows.every((r) => classifyActivitySchema.safeParse(r).success)) {
    suggestions.push("t_activity");
  }
  if (rows.every((r) => classifyContractSchema.safeParse(r).success)) {
    suggestions.push("t_payment_contract");
  }

  return suggestions.length ? suggestions : [];
};

const classifySingleFile = (
  fileIndex: number,
  fileName: string | undefined,
  rows: Record<string, unknown>[],
): FileClassificationResult => {
  try {
    const suggestedTables = classifyFileRows(rows);

    return {
      fileIndex,
      fileName,
      suggestedTables,
      rows,
      success: true,
    };
  } catch (error) {
    return {
      fileIndex,
      fileName,
      suggestedTables: [],
      rows: [],
      success: false,
      error:
        error instanceof Error ? error.message : "Unknown classification error",
    };
  }
};

export const classifyBatch = (
  request: BatchClassifyRequest,
): BatchClassifyResponse => {
  const results: FileClassificationResult[] = [];

  for (const file of request.files) {
    const result = classifySingleFile(file.fileIndex, file.fileName, file.rows);
    results.push(result);
  }

  return {
    results,
    totalFiles: request.files.length,
    completedFiles: results.filter((r) => r.success).length,
  };
};

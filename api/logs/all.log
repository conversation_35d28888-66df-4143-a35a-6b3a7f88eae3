2025-09-24 22:55:09:559 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 22:55:19:5519 [31merror[39m: [31mRoute  not found[39m
2025-09-24 22:55:19:5519 [35mhttp[39m: [35mGET / 404 183 - 5.628 ms[39m
[35m[39m
2025-09-24 22:55:19:5519 [31merror[39m: [31mRoute /favicon.ico not found[39m
2025-09-24 22:55:19:5519 [35mhttp[39m: [35mGET /favicon.ico 404 195 - 1.119 ms[39m
[35m[39m
2025-09-24 22:55:24:5524 [31merror[39m: [31mRoute /status not found[39m
2025-09-24 22:55:24:5524 [35mhttp[39m: [35mGET /status 404 190 - 1.302 ms[39m
[35m[39m
2025-09-24 22:55:59:5559 [35mhttp[39m: [35mGET /v1/public/status 200 2 - 3.730 ms[39m
[35m[39m
2025-09-24 22:56:48:5648 [31merror[39m: [31mRoute /v1/public/s not found[39m
2025-09-24 22:56:48:5648 [35mhttp[39m: [35mGET /v1/public/s 404 195 - 3.417 ms[39m
[35m[39m
2025-09-24 23:00:14:014 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:00:59:059 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:01:16:116 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:01:23:123 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:01:31:131 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:03:59:359 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:04:27:427 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:04:33:433 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:05:17:517 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:05:35:535 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:05:59:559 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:25:34:2534 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:25:40:2540 [35mhttp[39m: [35mGET /v1/public/farmer-groups 200 2 - 7.140 ms[39m
[35m[39m
2025-09-24 23:26:13:2613 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:26:25:2625 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 - - 6.135 ms[39m
[35m[39m
2025-09-24 23:26:31:2631 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:26:51:2651 [35mhttp[39m: [35mGET /v1/public/farmer-groups?efw=ef 200 2 - 6.136 ms[39m
[35m[39m
2025-09-24 23:27:06:276 [31merror[39m: [31mRoute /.well-known/appspecific/com.chrome.devtools.json not found[39m
2025-09-24 23:27:06:276 [35mhttp[39m: [35mGET /.well-known/appspecific/com.chrome.devtools.json 404 231 - 5.781 ms[39m
[35m[39m
2025-09-24 23:28:19:2819 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:28:51:2851 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:35:33:3533 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:36:03:363 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:38:43:3843 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:40:57:4057 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:41:20:4120 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:41:25:4125 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:41:29:4129 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:44:24:4424 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:44:44:4444 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:44:51:4451 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:48:00:480 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:48:18:4818 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:48:26:4826 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:48:31:4831 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:50:42:5042 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 - - 7.847 ms[39m
[35m[39m
2025-09-24 23:52:23:5223 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:52:29:5229 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:52:49:5249 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:53:05:535 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:53:17:5317 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:53:17:5317 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-24 23:53:17:5317 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 29.709 ms[39m
[35m[39m
2025-09-24 23:55:01:551 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:55:12:5512 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:55:12:5512 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-24 23:55:12:5512 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 30.466 ms[39m
[35m[39m
2025-09-24 23:55:24:5524 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-24 23:55:27:5527 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:55:27:5527 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-24 23:55:27:5527 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 27.529 ms[39m
[35m[39m
2025-09-25 11:33:05:335 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:33:18:3318 [31merror[39m: [31mSASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string[39m
2025-09-25 11:33:18:3318 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:33:18:3318 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 169 - 44.299 ms[39m
[35m[39m
2025-09-25 11:34:49:3449 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 169 - 1.040 ms[39m
[35m[39m
2025-09-25 11:34:50:3450 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 169 - 0.308 ms[39m
[35m[39m
2025-09-25 11:34:59:3459 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:35:02:352 [31merror[39m: [31mcould not determine data type of parameter $1[39m
2025-09-25 11:35:02:352 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:35:02:352 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 148 - 58.289 ms[39m
[35m[39m
2025-09-25 11:35:39:3539 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:35:48:3548 [31merror[39m: [31mop ANY/ALL (array) requires array on right side[39m
2025-09-25 11:35:48:3548 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:35:48:3548 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 150 - 52.194 ms[39m
[35m[39m
2025-09-25 11:35:58:3558 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:36:00:360 [35mhttp[39m: [35mGET /v1/public/farmer-groups 200 566 - 45.927 ms[39m
[35m[39m
2025-09-25 11:36:56:3656 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 566 - 1.232 ms[39m
[35m[39m
2025-09-25 11:36:57:3657 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 566 - 0.274 ms[39m
[35m[39m
2025-09-25 11:37:05:375 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:37:09:379 [35mhttp[39m: [35mGET /v1/public/farmer-groups 200 566 - 65.074 ms[39m
[35m[39m
2025-09-25 11:37:10:3710 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 566 - 0.766 ms[39m
[35m[39m
2025-09-25 11:37:20:3720 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:37:22:3722 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 - - 54.463 ms[39m
[35m[39m
2025-09-25 11:37:23:3723 [35mhttp[39m: [35mGET /v1/public/farmer-groups 304 - - 18.172 ms[39m
[35m[39m
2025-09-25 11:39:12:3912 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:39:23:3923 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-25 11:39:23:3923 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:39:23:3923 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 27.620 ms[39m
[35m[39m
2025-09-25 11:39:50:3950 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 1.275 ms[39m
[35m[39m
2025-09-25 11:39:51:3951 [35mhttp[39m: [35mGET /v1/public/farmer-groups 500 83 - 0.271 ms[39m
[35m[39m
2025-09-25 11:39:57:3957 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 11:40:00:400 [35mhttp[39m: [35mGET /v1/public/farmer-groups 200 410 - 65.389 ms[39m
[35m[39m
2025-09-25 13:28:57:2857 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:29:05:295 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:29:22:2922 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:30:29:3029 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:32:18:3218 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:34:50:3450 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:35:22:3522 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:35:31:3531 [31merror[39m: [31mRoute /classify not found[39m
2025-09-25 13:35:31:3531 [35mhttp[39m: [35mPOST /classify 404 192 - 9.060 ms[39m
[35m[39m
2025-09-25 13:37:58:3758 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:38:57:3857 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 13:39:01:391 [31merror[39m: [31mRoute /classify not found[39m
2025-09-25 13:39:01:391 [35mhttp[39m: [35mPOST /classify 404 191 - 9.682 ms[39m
[35m[39m
2025-09-25 13:39:11:3911 [35mhttp[39m: [35mPOST /v1/public/classify 200 120 - 4.199 ms[39m
[35m[39m
2025-09-25 13:40:46:4046 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:17:02:172 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 2.274 ms[39m
[35m[39m
2025-09-25 15:17:02:172 [31merror[39m: [31mROUTER ERROR: [[39m
[31m  {[39m
[31m    "expected": "array",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [],[39m
[31m    "message": "Invalid input: expected array, received object"[39m
[31m  }[39m
[31m][39m
2025-09-25 15:17:02:172 [31merror[39m: [31mZod Error[39m
2025-09-25 15:17:02:172 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 34.088 ms[39m
[35m[39m
2025-09-25 15:17:34:1734 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.975 ms[39m
[35m[39m
2025-09-25 15:17:34:1734 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 2.161 ms[39m
[35m[39m
2025-09-25 15:17:50:1750 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.362 ms[39m
[35m[39m
2025-09-25 15:17:50:1750 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 0.674 ms[39m
[35m[39m
2025-09-25 15:18:48:1848 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.696 ms[39m
[35m[39m
2025-09-25 15:18:48:1848 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 1.513 ms[39m
[35m[39m
2025-09-25 15:19:12:1912 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.204 ms[39m
[35m[39m
2025-09-25 15:19:12:1912 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 0.591 ms[39m
[35m[39m
2025-09-25 15:19:34:1934 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.264 ms[39m
[35m[39m
2025-09-25 15:19:34:1934 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 0.731 ms[39m
[35m[39m
2025-09-25 15:20:27:2027 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.784 ms[39m
[35m[39m
2025-09-25 15:20:27:2027 [35mhttp[39m: [35mPOST /v1/public/classify 400 158 - 6.024 ms[39m
[35m[39m
2025-09-25 15:20:57:2057 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:21:13:2113 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.347 ms[39m
[35m[39m
2025-09-25 15:21:13:2113 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:21:13:2113 [35mhttp[39m: [35mPOST /v1/public/classify 500 42 - 21.281 ms[39m
[35m[39m
2025-09-25 15:21:42:2142 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:21:48:2148 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.337 ms[39m
[35m[39m
2025-09-25 15:21:48:2148 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:21:48:2148 [35mhttp[39m: [35mPOST /v1/public/classify 500 42 - 45.292 ms[39m
[35m[39m
2025-09-25 15:22:44:2244 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:22:48:2248 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.349 ms[39m
[35m[39m
2025-09-25 15:22:48:2248 [31merror[39m: [31mROUTER ERROR: Invalid element at key "~standard": expected a Zod schema[39m
2025-09-25 15:22:48:2248 [35mhttp[39m: [35mPOST /v1/public/classify 500 73 - 19.696 ms[39m
[35m[39m
2025-09-25 15:23:41:2341 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:23:59:2359 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:24:03:243 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.447 ms[39m
[35m[39m
2025-09-25 15:24:03:243 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:24:03:243 [35mhttp[39m: [35mPOST /v1/public/classify 500 42 - 20.647 ms[39m
[35m[39m
2025-09-25 15:30:22:3022 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:31:43:3143 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:31:49:3149 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:32:57:3257 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.373 ms[39m
[35m[39m
2025-09-25 15:32:57:3257 [31merror[39m: [31mROUTER ERROR: [[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      0[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  },[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      1[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  },[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      2[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  }[39m
[31m][39m
2025-09-25 15:32:57:3257 [31merror[39m: [31mZod Error[39m
2025-09-25 15:32:57:3257 [35mhttp[39m: [35mPOST /v1/public/classify 400 412 - 28.149 ms[39m
[35m[39m
2025-09-25 15:37:46:3746 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:37:57:3757 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:38:06:386 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:38:16:3816 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:38:58:3858 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:39:11:3911 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:39:21:3921 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:39:30:3930 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:40:31:4031 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:42:01:421 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:42:28:4228 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:43:02:432 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.429 ms[39m
[35m[39m
2025-09-25 15:43:02:432 [35mhttp[39m: [35mPOST /v1/public/classify 200 2258 - 19.253 ms[39m
[35m[39m
2025-09-25 15:44:51:4451 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.651 ms[39m
[35m[39m
2025-09-25 15:44:51:4451 [35mhttp[39m: [35mPOST /v1/public/classify 200 2258 - 4.264 ms[39m
[35m[39m
2025-09-25 15:48:30:4830 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:48:37:4837 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 2.777 ms[39m
[35m[39m
2025-09-25 15:48:37:4837 [35mhttp[39m: [35mPOST /v1/public/classify 200 2297 - 36.880 ms[39m
[35m[39m
2025-09-25 15:52:14:5214 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.597 ms[39m
[35m[39m
2025-09-25 15:52:14:5214 [35mhttp[39m: [35mPOST /v1/public/classify 200 2297 - 2.520 ms[39m
[35m[39m
2025-09-25 15:52:27:5227 [32minfo[39m: [32mACR API running at http://localhost:3000[39m
2025-09-25 15:52:38:5238 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 1.444 ms[39m
[35m[39m
2025-09-25 15:52:38:5238 [35mhttp[39m: [35mPOST /v1/public/classify 200 2298 - 21.311 ms[39m
[35m[39m
2025-09-25 15:54:47:5447 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.577 ms[39m
[35m[39m
2025-09-25 15:54:47:5447 [35mhttp[39m: [35mPOST /v1/public/classify 200 2298 - 2.158 ms[39m
[35m[39m
2025-09-25 15:55:05:555 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.175 ms[39m
[35m[39m
2025-09-25 15:55:05:555 [35mhttp[39m: [35mPOST /v1/public/classify 200 2298 - 0.653 ms[39m
[35m[39m
2025-09-25 15:55:17:5517 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.434 ms[39m
[35m[39m
2025-09-25 15:55:17:5517 [35mhttp[39m: [35mPOST /v1/public/classify 200 2298 - 1.706 ms[39m
[35m[39m
2025-09-25 16:08:52:852 [35mhttp[39m: [35mOPTIONS /v1/public/classify 204 0 - 0.391 ms[39m
[35m[39m
2025-09-25 16:08:52:852 [35mhttp[39m: [35mPOST /v1/public/classify 200 2298 - 5.450 ms[39m
[35m[39m
2025-09-25 16:09:27:927 [32minfo[39m: [32mACR API running at http://localhost:3000[39m

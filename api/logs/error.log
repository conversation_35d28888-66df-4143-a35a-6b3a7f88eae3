2025-09-24 22:55:19:5519 [31merror[39m: [31mRoute  not found[39m
2025-09-24 22:55:19:5519 [31merror[39m: [31mRoute /favicon.ico not found[39m
2025-09-24 22:55:24:5524 [31merror[39m: [31mRoute /status not found[39m
2025-09-24 22:56:48:5648 [31merror[39m: [31mRoute /v1/public/s not found[39m
2025-09-24 23:27:06:276 [31merror[39m: [31mRoute /.well-known/appspecific/com.chrome.devtools.json not found[39m
2025-09-24 23:53:17:5317 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:53:17:5317 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-24 23:55:12:5512 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:55:12:5512 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-24 23:55:27:5527 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-24 23:55:27:5527 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:33:18:3318 [31merror[39m: [31mSASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string[39m
2025-09-25 11:33:18:3318 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:35:02:352 [31merror[39m: [31mcould not determine data type of parameter $1[39m
2025-09-25 11:35:02:352 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:35:48:3548 [31merror[39m: [31mop ANY/ALL (array) requires array on right side[39m
2025-09-25 11:35:48:3548 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 11:39:23:3923 [31merror[39m: [31mAggregateError[39m
[31m    at /Users/<USER>/Documents/code/terraspect/ACR/node_modules/pg-pool/index.js:45:11[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5) {[39m
[31m  code: 'ECONNREFUSED',[39m
[31m  [errors]: [[39m
[31m    Error: connect ECONNREFUSED ::1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '::1',[39m
[31m      port: 5432[39m
[31m    },[39m
[31m    Error: connect ECONNREFUSED 127.0.0.1:5432[39m
[31m        at createConnectionError (node:net:1678:14)[39m
[31m        at afterConnectMultiple (node:net:1708:16) {[39m
[31m      errno: -61,[39m
[31m      code: 'ECONNREFUSED',[39m
[31m      syscall: 'connect',[39m
[31m      address: '127.0.0.1',[39m
[31m      port: 5432[39m
[31m    }[39m
[31m  ][39m
[31m}[39m
2025-09-25 11:39:23:3923 [31merror[39m: [31mROUTER ERROR: Server error. Please contact provider.[39m
2025-09-25 13:35:31:3531 [31merror[39m: [31mRoute /classify not found[39m
2025-09-25 13:39:01:391 [31merror[39m: [31mRoute /classify not found[39m
2025-09-25 15:17:02:172 [31merror[39m: [31mROUTER ERROR: [[39m
[31m  {[39m
[31m    "expected": "array",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [],[39m
[31m    "message": "Invalid input: expected array, received object"[39m
[31m  }[39m
[31m][39m
2025-09-25 15:17:02:172 [31merror[39m: [31mZod Error[39m
2025-09-25 15:21:13:2113 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:21:48:2148 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:22:48:2248 [31merror[39m: [31mROUTER ERROR: Invalid element at key "~standard": expected a Zod schema[39m
2025-09-25 15:24:03:243 [31merror[39m: [31mROUTER ERROR: rows.every is not a function[39m
2025-09-25 15:32:57:3257 [31merror[39m: [31mROUTER ERROR: [[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      0[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  },[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      1[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  },[39m
[31m  {[39m
[31m    "expected": "record",[39m
[31m    "code": "invalid_type",[39m
[31m    "path": [[39m
[31m      "files",[39m
[31m      2[39m
[31m    ],[39m
[31m    "message": "Invalid input: expected record, received array"[39m
[31m  }[39m
[31m][39m
2025-09-25 15:32:57:3257 [31merror[39m: [31mZod Error[39m
